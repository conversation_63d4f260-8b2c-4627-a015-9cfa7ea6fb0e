"use client"

import { useEffect, useRef, useCallback, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  X,
  Music2,
  Waves
} from "lucide-react"
import { useAudioStore } from "@/lib/audio-store"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"

export function GlobalAudioPlayer() {
  const {
    globalPlayer,
    stopGlobalPlayer,
    setGlobalPlayerPlaying,
    setGlobalPlayerTime,
    setGlobalPlayerVolume,
    toggleGlobalPlayerMute,
  } = useAudioStore()

  const audioRef = useRef<HTMLAudioElement | null>(null)

  // Use global state for synchronization
  const isPlaying = globalPlayer.isPlaying
  const currentTime = globalPlayer.currentTime
  const duration = globalPlayer.duration
  const volume = globalPlayer.volume
  const isMuted = globalPlayer.isMuted

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // Handle time update event (sync with global state)
  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current) {
      setGlobalPlayerTime(audioRef.current.currentTime, audioRef.current.duration)
    }
  }, [setGlobalPlayerTime])

  // Handle loaded metadata event (sync with global state)
  const handleLoadedMetadata = useCallback(() => {
    if (audioRef.current) {
      setGlobalPlayerTime(audioRef.current.currentTime, audioRef.current.duration)
    }
  }, [setGlobalPlayerTime])

  // Handle play/pause toggle (sync with global state)
  const handlePlayPause = useCallback(() => {
    if (!audioRef.current) return

    if (isPlaying) {
      audioRef.current.pause()
      setGlobalPlayerPlaying(false)
    } else {
      audioRef.current.play().catch(error => {
        console.error("Play failed:", error)
        setGlobalPlayerPlaying(false)
      })
    }
  }, [isPlaying, setGlobalPlayerPlaying])

  // Handle volume toggle
  const handleVolumeToggle = useCallback(() => {
    toggleGlobalPlayerMute()
  }, [toggleGlobalPlayerMute])

  // Handle seeking in the timeline
  const handleSeek = useCallback((value: number[]) => {
    const newTime = value[0]
    if (audioRef.current) {
      audioRef.current.currentTime = newTime
      setGlobalPlayerTime(newTime, duration)
    }
  }, [setGlobalPlayerTime, duration])

  // Handle close player
  const handleClose = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause()
    }
    stopGlobalPlayer()
  }, [stopGlobalPlayer])

  // Create/update audio element and set up event listeners (like admin player)
  useEffect(() => {
    if (!globalPlayer.currentTrack?.src) {
      setIsPlaying(false)
      return
    }

    // Create audio element if it doesn't exist
    if (!audioRef.current) {
      audioRef.current = new Audio()

      // Set up event listeners for the audio element (sync with global state)
      audioRef.current.addEventListener("play", () => setGlobalPlayerPlaying(true))
      audioRef.current.addEventListener("pause", () => setGlobalPlayerPlaying(false))
      audioRef.current.addEventListener("ended", () => {
        setGlobalPlayerPlaying(false)
        stopGlobalPlayer()
      })
      audioRef.current.addEventListener("timeupdate", handleTimeUpdate)
      audioRef.current.addEventListener("loadedmetadata", handleLoadedMetadata)
    }

    // Update audio source if it changed
    if (audioRef.current.src !== globalPlayer.currentTrack.src) {
      audioRef.current.src = globalPlayer.currentTrack.src
      audioRef.current.load()
    }

    // Play the audio
    const playPromise = audioRef.current.play()

    // Handle autoplay restrictions
    if (playPromise !== undefined) {
      playPromise.catch(error => {
        console.error("Autoplay prevented:", error)
        setGlobalPlayerPlaying(false)
      })
    }

    // Update volume & muted state
    audioRef.current.volume = isMuted ? 0 : volume / 100

    // Cleanup function
    return () => {
      if (audioRef.current) {
        audioRef.current.removeEventListener("play", () => setGlobalPlayerPlaying(true))
        audioRef.current.removeEventListener("pause", () => setGlobalPlayerPlaying(false))
        audioRef.current.removeEventListener("ended", () => {
          setGlobalPlayerPlaying(false)
          stopGlobalPlayer()
        })
        audioRef.current.removeEventListener("timeupdate", handleTimeUpdate)
        audioRef.current.removeEventListener("loadedmetadata", handleLoadedMetadata)
        audioRef.current.pause()
      }
    }
  }, [globalPlayer.currentTrack?.src, stopGlobalPlayer, setGlobalPlayerPlaying, isMuted, volume, handleTimeUpdate, handleLoadedMetadata])

  // Update volume when it changes (sync with global state)
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume / 100
    }
  }, [volume, isMuted])

  if (!globalPlayer.currentTrack) {
    return null
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 100, opacity: 0 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className="fixed bottom-0 left-0 right-0 z-[9999] bg-background/95 backdrop-blur-sm border-t border-border shadow-lg"
      >
        <div className="w-full max-w-[120rem] mx-auto p-4">
          <div className="flex items-center gap-4">
            {/* Play/Pause Button */}
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-10 w-10 rounded-full shrink-0 transition-all duration-200",
                isPlaying
                  ? "bg-gradient-to-r from-orange-500 to-rose-500 text-white hover:from-orange-600 hover:to-rose-600"
                  : "hover:bg-muted"
              )}
              onClick={handlePlayPause}
            >
              {isPlaying ? (
                <Pause className="h-4 w-4" />
              ) : (
                <Play className="h-4 w-4 ml-0.5" />
              )}
            </Button>

            {/* Track Icon */}
            <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-orange-100 to-rose-100 dark:from-orange-900/30 dark:to-rose-900/30 flex items-center justify-center shrink-0">
              {globalPlayer.currentTrack.type === "music" ? (
                <Music2 className="h-4 w-4 text-orange-600 dark:text-orange-400" />
              ) : (
                <Waves className="h-4 w-4 text-orange-600 dark:text-orange-400" />
              )}
            </div>

            {/* Track Info */}
            <div className="flex-1 min-w-0 max-w-48">
              <div className="truncate font-medium text-sm">
                {globalPlayer.currentTrack.title}
              </div>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                {globalPlayer.currentTrack.type === "music" && globalPlayer.currentTrack.genres && globalPlayer.currentTrack.genres.length > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {globalPlayer.currentTrack.genres[0]}
                  </Badge>
                )}
                {globalPlayer.currentTrack.type === "nature-sound" && globalPlayer.currentTrack.category && globalPlayer.currentTrack.category.length > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {globalPlayer.currentTrack.category[0]}
                  </Badge>
                )}
              </div>
            </div>

            {/* Enhanced Progress Bar with Time Display */}
            <div className="hidden md:flex flex-1 max-w-md items-center gap-3">
              <span className="text-xs text-muted-foreground font-mono min-w-[35px]">
                {formatTime(currentTime)}
              </span>
              <Slider
                value={[currentTime]}
                min={0}
                max={duration || 100}
                step={0.1}
                onValueChange={handleSeek}
                className="cursor-pointer flex-1"
              />
              <span className="text-xs text-muted-foreground font-mono min-w-[35px]">
                {formatTime(duration)}
              </span>
            </div>

            {/* Volume Controls */}
            <div className="flex items-center gap-2 shrink-0">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={handleVolumeToggle}
              >
                {isMuted ? (
                  <VolumeX className="h-4 w-4" />
                ) : (
                  <Volume2 className="h-4 w-4" />
                )}
              </Button>
              <Slider
                value={[isMuted ? 0 : volume]}
                min={0}
                max={100}
                step={1}
                onValueChange={(value) => {
                  setGlobalPlayerVolume(value[0])
                }}
                className="w-20"
              />
            </div>

            {/* Close Button */}
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={handleClose}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Mobile Progress Bar with Time Display */}
          <div className="md:hidden mt-3">
            <div className="flex items-center gap-3">
              <span className="text-xs text-muted-foreground font-mono min-w-[35px]">
                {formatTime(currentTime)}
              </span>
              <Slider
                value={[currentTime]}
                min={0}
                max={duration || 100}
                step={0.1}
                onValueChange={handleSeek}
                className="cursor-pointer flex-1"
              />
              <span className="text-xs text-muted-foreground font-mono min-w-[35px]">
                {formatTime(duration)}
              </span>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}
