"use client"

import { useState, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"
import {
  Search,
  Plus,
  Music2,
  Star,
  Filter,
  X,
  Play,
  Pause
} from "lucide-react"
import { useGetMusics } from "@schemas/Music/music-query"
import { useAddMusicToMusicPlaylistUser } from "@schemas/MusicPlaylist/music-playlist-user-query"
import { useAudioStore } from "@/lib/audio-store"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { AddToPlaylistDialog } from "../../_components/add-to-playlist-dialog"

interface Music {
  id: string
  title: string
  src: string | null
  genres: string[]
  isPublic: boolean
  rating?: number | null
}

export function MusicsRoute() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedGenres, setSelectedGenres] = useState<string[]>([])
  const [selectedMusicId, setSelectedMusicId] = useState<string | null>(null)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)

  const { data: musics, isLoading } = useGetMusics({ isPublic: true })
  const addMusicToPlaylist = useAddMusicToMusicPlaylistUser()

  // Global audio player state
  const {
    globalPlayer,
    setGlobalPlayerTrack,
    setGlobalPlayerPlaying
  } = useAudioStore()

  // Get unique genres for filtering
  const availableGenres = useMemo(() => {
    if (!musics) return []
    const genreSet = new Set<string>()
    musics.forEach(music => {
      music.genres?.forEach(genre => genreSet.add(genre))
    })
    return Array.from(genreSet).sort()
  }, [musics])

  // Filter musics based on search and genre filters
  const filteredMusics = useMemo(() => {
    if (!musics) return []
    
    return musics.filter(music => {
      const matchesSearch = music.title.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesGenre = selectedGenres.length === 0 || 
        music.genres?.some(genre => selectedGenres.includes(genre))
      
      return matchesSearch && matchesGenre
    })
  }, [musics, searchQuery, selectedGenres])

  const handleGenreToggle = (genre: string) => {
    setSelectedGenres(prev => 
      prev.includes(genre) 
        ? prev.filter(g => g !== genre)
        : [...prev, genre]
    )
  }

  const clearFilters = () => {
    setSearchQuery("")
    setSelectedGenres([])
  }

  const handleAddToPlaylist = (musicId: string) => {
    setSelectedMusicId(musicId)
    setIsAddDialogOpen(true)
  }

  const handleAddMusicToPlaylist = async (playlistId: string) => {
    if (!selectedMusicId) return

    await addMusicToPlaylist.mutateAsync({
      musicPlaylistUserId: playlistId,
      musicIds: [selectedMusicId]
    })
  }

  const handlePlayMusic = (music: Music) => {
    const track = {
      id: music.id,
      title: music.title,
      src: music.src || `https://www.soundjay.com/misc/sounds/bell-ringing-05.wav`,
      type: 'music' as const,
      genres: music.genres
    }

    if (globalPlayer.currentTrack?.id === music.id) {
      // If already playing this track, toggle play/pause
      setGlobalPlayerPlaying(!globalPlayer.isPlaying)
    } else {
      // Play new track
      setGlobalPlayerTrack(track)
    }
  }

  const selectedMusic = selectedMusicId ? musics?.find(m => m.id === selectedMusicId) : null

  return (
    <>
      <div className="space-y-6 pb-32">
        {/* Header Section */}
        <div className="space-y-4">
          <div className="relative">
            {/* Decorative background glow */}
            <div className="absolute -inset-2 bg-gradient-to-r from-red-500/10 via-rose-500/5 to-red-500/10 rounded-xl blur-xl opacity-60 dark:opacity-40" />

            <div className="relative">
              <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-red-600 to-rose-600 dark:from-red-400 dark:to-rose-400 bg-clip-text text-transparent">
                Musics
              </h1>
              <p className="text-muted-foreground mt-1">
                Discover and add music tracks to enhance your focus sessions
              </p>
            </div>
          </div>

          {/* Search and Filters Card */}
          <Card className="border-red-200/50 dark:border-red-800/20 bg-gradient-to-br from-red-50/30 to-rose-50/20 dark:from-red-950/10 dark:to-rose-950/5">
            <CardContent className="p-4 space-y-4">
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search music tracks..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 border-red-200/30 dark:border-red-800/30 focus:border-red-400 dark:focus:border-red-600"
                  />
                </div>

                {(searchQuery || selectedGenres.length > 0) && (
                  <Button
                    variant="outline"
                    onClick={clearFilters}
                    className="gap-2 shrink-0 border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-950/20"
                  >
                    <X className="h-4 w-4" />
                    Clear Filters
                  </Button>
                )}
              </div>

              {/* Genre Filters */}
              {availableGenres.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4 text-red-600 dark:text-red-400" />
                    <span className="text-sm font-medium text-red-700 dark:text-red-300">Genres</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {availableGenres.map(genre => (
                      <Badge
                        key={genre}
                        variant={selectedGenres.includes(genre) ? "default" : "outline"}
                        className={cn(
                          "cursor-pointer transition-all duration-200 hover:scale-105 border-red-200 dark:border-red-800",
                          selectedGenres.includes(genre)
                            ? "bg-gradient-to-r from-red-500 to-rose-500 text-white border-red-400 dark:border-red-600 hover:from-red-600 hover:to-rose-600"
                            : "text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-950/20"
                        )}
                        onClick={() => handleGenreToggle(genre)}
                      >
                        {genre}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Music List */}
        <div className="space-y-3">
          {isLoading ? (
            <div className="space-y-3">
              {Array.from({ length: 8 }).map((_, i) => (
                <motion.div
                  key={i}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.2, delay: i * 0.03 }}
                >
                  <Card className="border-red-200/30 dark:border-red-800/20">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-4">
                        <Skeleton className="h-12 w-12 rounded-lg shrink-0" />
                        <div className="flex-1 space-y-2">
                          <Skeleton className="h-4 w-2/3" />
                          <div className="flex gap-2">
                            <Skeleton className="h-3 w-16" />
                            <Skeleton className="h-3 w-20" />
                            <Skeleton className="h-3 w-12" />
                          </div>
                        </div>
                        <div className="flex items-center gap-3 shrink-0">
                          <Skeleton className="h-6 w-16" />
                          <Skeleton className="h-8 w-28" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          ) : filteredMusics.length === 0 ? (
            <div className="text-center py-12">
              <div className="p-4 rounded-full bg-gradient-to-br from-red-100 to-rose-100 dark:from-red-900/30 dark:to-rose-900/30 w-fit mx-auto mb-4">
                <Music2 className="h-8 w-8 text-red-600 dark:text-red-400" />
              </div>
              <h3 className="font-semibold text-lg mb-2">No music tracks found</h3>
              <p className="text-muted-foreground">
                {searchQuery || selectedGenres.length > 0
                  ? "Try adjusting your search or filters"
                  : "No music tracks are available at the moment"
                }
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              <AnimatePresence mode="popLayout">
                {filteredMusics.map((music) => {
                  const isCurrentlyPlaying = globalPlayer.currentTrack?.id === music.id && globalPlayer.isPlaying
                  const isCurrentTrack = globalPlayer.currentTrack?.id === music.id

                  return (
                    <motion.div
                      key={music.id}
                      initial={{ opacity: 0, scale: 0.98 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.98 }}
                      transition={{
                        duration: 0.15,
                        ease: "easeOut"
                      }}
                      className="will-change-transform"
                    >
                      <Card
                        className={cn(
                          "group relative overflow-hidden transition-all duration-200 cursor-pointer",
                          "border-red-200/30 dark:border-red-800/20 hover:border-red-300 dark:hover:border-red-700",
                          "hover:shadow-md hover:shadow-red-500/5",
                          isCurrentTrack && "bg-red-50/50 dark:bg-red-950/20 border-red-300 dark:border-red-700 ring-1 ring-red-500/20"
                        )}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center gap-4">
                            {/* Music Icon / Play Button */}
                            <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-red-100 to-rose-100 dark:from-red-900/30 dark:to-rose-900/30 flex items-center justify-center shrink-0 relative">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handlePlayMusic(music)
                                }}
                                className="w-12 h-12 rounded-lg hover:bg-red-200 dark:hover:bg-red-800 absolute inset-0 transition-colors duration-150"
                              >
                                {isCurrentlyPlaying ? (
                                  <Pause className="h-5 w-5 text-red-600 dark:text-red-400" />
                                ) : (
                                  <Play className="h-5 w-5 text-red-600 dark:text-red-400 ml-0.5" />
                                )}
                              </Button>
                            </div>

                            {/* Music Info */}
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium line-clamp-1 mb-2 text-base leading-tight">{music.title}</h3>
                              <div className="flex items-center gap-2 flex-wrap">
                                {music.genres?.slice(0, 3).map(genre => (
                                  <Badge
                                    key={genre}
                                    variant="secondary"
                                    className="text-xs px-2 py-0.5 bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300 border-red-200 dark:border-red-800"
                                  >
                                    {genre}
                                  </Badge>
                                ))}
                                {music.rating && (
                                  <div className="flex items-center gap-1">
                                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                    <span className="text-xs text-muted-foreground">
                                      {music.rating.toFixed(1)}
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Add Button */}
                            <div className="shrink-0">
                              <Button
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleAddToPlaylist(music.id)
                                }}
                                size="sm"
                                className={cn(
                                  "h-8 px-4 text-xs transition-all duration-200 gap-2",
                                  "bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 text-white",
                                  "shadow-sm hover:shadow-md"
                                )}
                              >
                                <Plus className="h-3 w-3" />
                                Add to Playlist
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  )
                })}
              </AnimatePresence>
            </div>
          )}
        </div>

      {/* Add to Playlist Dialog */}
      <AddToPlaylistDialog
        isOpen={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        itemType="music"
        itemTitle={selectedMusic?.title || ""}
        onAddToPlaylist={handleAddMusicToPlaylist}
      />
      </div>
    </>
  )
}
