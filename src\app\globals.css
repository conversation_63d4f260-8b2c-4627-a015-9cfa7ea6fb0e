@import "tailwindcss";
@import "tw-animate-css";

@source "../../node_modules/@daveyplate/better-auth-ui";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.6 0.21 25);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.65 0.25 25);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.65 0.25 25);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Optimized sidebar transitions */
@layer components {
  .sidebar-transition-optimized {
    transition-property: transform, width, left, right, margin;
    transition-duration: 300ms;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, width, left, right, margin;
    transform: translate3d(0, 0, 0); /* Hardware acceleration */
  }

  .sidebar-content-transition {
    transition-property: opacity, transform;
    transition-duration: 250ms;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
  }

  /* CSS containment for better scroll performance */
  .contain-layout {
    contain: layout;
  }

  .contain-style {
    contain: style;
  }

  .contain-layout-style {
    contain: layout style;
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    .sidebar-transition-optimized,
    .sidebar-content-transition {
      transition-duration: 0ms;
      will-change: auto;
    }
  }

  /* Timer page mobile optimizations */
  .timer-page-container {
    /* Prevent unwanted scrolling on mobile */
    position: fixed;
    touch-action: manipulation;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: none;
  }

  /* Music control optimizations for scrolling and layering */
  .music-control-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    touch-action: pan-y;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
  }

  .music-control-scroll::-webkit-scrollbar {
    width: 4px;
  }

  .music-control-scroll::-webkit-scrollbar-track {
    background: transparent;
  }

  .music-control-scroll::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    border: none;
  }

  .music-control-scroll::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }

  /* Ensure music control appears above timer */
  .music-control-container {
    z-index: 1002;
    position: fixed;
    contain: layout style;
  }

  /* Mobile touch target improvements */
  @media (max-width: 768px) {
    /* Ensure touch targets are at least 44px */
    .timer-page-container button {
      min-height: 44px;
      min-width: 44px;
    }

    /* Override for smaller nature sound play/pause buttons */
    .timer-page-container .nature-sound-play-btn {
      min-height: auto !important;
      min-width: auto !important;
    }

    /* Prevent zooming on double tap */
    .timer-page-container * {
      touch-action: manipulation;
    }

    /* Hide scrollbars on mobile timer page only */
    .timer-page-container {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }

    .timer-page-container::-webkit-scrollbar {
      display: none;
    }

    /* Ensure task sheet scrollbars are always visible */
    .timer-page-container .task-sheet-scroll {
      -ms-overflow-style: auto !important;
      scrollbar-width: thin !important;
    }

    .timer-page-container .task-sheet-scroll::-webkit-scrollbar {
      display: block !important;
      width: 6px !important;
    }
  }

  /* Improved mobile gesture detection */
  .gesture-area {
    touch-action: pan-x pan-y;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  /* Mobile-friendly controls */
  .mobile-controls {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }

  /* Prevent rubber band scrolling on iOS */
  body.has-timer {
    position: fixed;
    width: 100%;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile fullscreen improvements */
  @media (max-width: 768px) {
    .fullscreen-prompt {
      position: fixed;
      bottom: 1rem;
      left: 1rem;
      right: 1rem;
      width: auto;
      transform: none;
    }
  }

  /* Safe area handling for devices with notches */
  @supports (padding: max(0px)) {
    .timer-page-container {
      padding-top: max(1rem, env(safe-area-inset-top));
      padding-bottom: max(1rem, env(safe-area-inset-bottom));
      padding-left: max(1rem, env(safe-area-inset-left));
      padding-right: max(1rem, env(safe-area-inset-right));
    }

    /* Adjust controls for safe areas */
    .timer-controls-top {
      top: max(0.75rem, env(safe-area-inset-top));
    }

    .timer-controls-bottom {
      bottom: max(1rem, env(safe-area-inset-bottom));
    }
  }
}

/* Custom animations */
@keyframes indicator-appear {
  0% {
    opacity: 0;
    transform: translateX(8px);
  }
  10% {
    opacity: 1;
    transform: translateX(0);
  }
  90% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(8px);
  }
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* Custom scrollbar utilities */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.2) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground) / 0.2);
  border-radius: 2px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground) / 0.3);
}

/* Task Sheet specific scrollbar styles */
.task-sheet-scroll {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
  overflow-y: auto !important;
  overflow-x: hidden;
}

.task-sheet-scroll::-webkit-scrollbar {
  width: 6px;
  display: block !important;
}

.task-sheet-scroll::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.3);
  border-radius: 3px;
}

.task-sheet-scroll::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground) / 0.4);
  border-radius: 3px;
  border: 1px solid hsl(var(--background));
}

.task-sheet-scroll::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground) / 0.6);
}

.task-sheet-scroll::-webkit-scrollbar-thumb:active {
  background-color: hsl(var(--muted-foreground) / 0.7);
}

/* Task Sheet mobile scrollbar optimization */
@media (max-width: 768px) {
  .task-sheet-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  .task-sheet-scroll::-webkit-scrollbar {
    width: 4px;
  }

  .task-sheet-scroll::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground) / 0.5);
    border: none;
  }
}

/* Playlist Form Sheet scrollbar styles */
.playlist-form-scroll {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
  overflow-y: auto !important;
  overflow-x: hidden;
}

.playlist-form-scroll::-webkit-scrollbar {
  width: 6px;
  display: block !important;
}

.playlist-form-scroll::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.3);
  border-radius: 3px;
}

.playlist-form-scroll::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground) / 0.4);
  border-radius: 3px;
  border: 1px solid hsl(var(--background));
}

.playlist-form-scroll::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground) / 0.6);
}

.playlist-form-scroll::-webkit-scrollbar-thumb:active {
  background-color: hsl(var(--muted-foreground) / 0.7);
}

/* Playlist Form Sheet mobile scrollbar optimization */
@media (max-width: 768px) {
  .playlist-form-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  .playlist-form-scroll::-webkit-scrollbar {
    width: 4px;
  }

  .playlist-form-scroll::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground) / 0.5);
    border: none;
  }
}

.scrollbar-thumb-muted-foreground\/20::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground) / 0.2);
}

.scrollbar-track-transparent::-webkit-scrollbar-track {
  background: transparent;
}

@keyframes fadeSlideUp {
  0% {
    opacity: 0;
    transform: translateY(8px);
  }
  20% {
    opacity: 0.2;
    transform: translateY(6px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Button animations */
@keyframes subtle-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.01);
  }
}

@keyframes subtle-glow {
  0%,
  100% {
    opacity: 0;
  }
  50% {
    opacity: 0.08;
  }
}

@keyframes progress-pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

.animate-subtle-pulse {
  animation: subtle-pulse 3s ease-in-out infinite;
}

.animate-subtle-glow {
  animation: subtle-glow 3s ease-in-out infinite;
}

.animate-progress {
  animation: progress-pulse 1.5s infinite;
}

/* Marquee animation for music player */
.marquee-wrapper {
  display: block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
}

@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-marquee {
  display: inline-block;
  animation: marquee 15s linear infinite;
}

/* Slow pulse animation for timer preview */
@keyframes pulse-slow {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Music Control Enhanced Animations */
@keyframes music-bounce {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes tab-slide-in {
  0% {
    transform: translateY(-10px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes volume-expand {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 100%;
    opacity: 1;
  }
}

@keyframes player-glow {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(var(--primary), 0);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(var(--primary), 0.1);
  }
}

.animate-music-bounce {
  animation: music-bounce 0.6s ease-in-out;
}

.animate-tab-slide-in {
  animation: tab-slide-in 0.3s ease-out;
}

.animate-volume-expand {
  animation: volume-expand 0.2s ease-out;
}

.animate-player-glow {
  animation: player-glow 2s ease-in-out infinite;
}

/* Enhanced transitions for music controls */
.music-control-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, background-color, opacity;
}

.music-control-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.music-tab-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth height transitions for tab content */
.tab-content-transition {
  transition:
    height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.2s ease-out;
}

/* Performance optimizations for music controls */
.music-control-optimized {
  backface-visibility: hidden;
  transform: translate3d(0, 0, 0);
  will-change: transform;
}

/* Mobile-specific enhancements */
@layer components {
  @media (max-width: 768px) {
    /* Enhanced touch targets for music controls */
    .music-control-mobile button {
      min-height: 44px;
      min-width: 44px;
      touch-action: manipulation;
      -webkit-tap-highlight-color: transparent;
    }

    /* Override for smaller nature sound play/pause buttons */
    .nature-sound-play-btn {
      min-height: auto !important;
      min-width: auto !important;
    }

    /* Better slider handling on mobile */
    .music-control-mobile [role="slider"] {
      touch-action: pan-x;
      min-height: 44px;
    }

    /* Improved tab navigation on mobile */
    .music-control-mobile [role="tab"] {
      min-height: 48px;
      touch-action: manipulation;
      -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }

    /* Better spacing for mobile content */
    .music-control-mobile .tab-content {
      padding: 1rem;
      touch-action: pan-y;
    }

    /* Enhanced focus states for mobile */
    .music-control-mobile button:focus-visible {
      outline: 2px solid hsl(var(--primary));
      outline-offset: 2px;
    }

    /* Prevent text selection during interactions */
    .music-control-mobile {
      -webkit-user-select: none;
      user-select: none;
      -webkit-touch-callout: none;
    }

    /* Allow text selection for song titles */
    .music-control-mobile .track-title {
      -webkit-user-select: text;
      user-select: text;
    }

    /* Smoother tab transitions on mobile */
    .music-tab-mobile {
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      will-change: transform, background-color;
    }

    .music-tab-mobile:active {
      transform: scale(0.98);
    }

    /* Enhanced button feedback on mobile */
    .music-button-mobile {
      transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
      will-change: transform, background-color;
    }

    .music-button-mobile:active {
      transform: scale(0.95);
    }

    /* Better collapsible animations on mobile */
    .music-collapsible-mobile {
      transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      will-change: max-height;
    }
  }

  /* Progressive Web App enhancements */
  @media (display-mode: standalone) {
    /* PWA specific styles for music controls */
    .music-control-pwa {
      padding-bottom: env(safe-area-inset-bottom);
    }
  }
}

/* Enhanced accessibility for music controls */
@media (prefers-reduced-motion: reduce) {
  .animate-marquee,
  .animate-music-bounce,
  .animate-tab-slide-in,
  .animate-volume-expand,
  .animate-player-glow,
  .animate-mobile-tab-press,
  .animate-mobile-button-ripple {
    animation: none;
  }

  .music-control-transition,
  .music-tab-transition,
  .tab-content-transition {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .music-control-mobile button {
    border: 2px solid;
  }

  .music-control-mobile [role="slider"] {
    outline: 2px solid;
  }
}

/* Dark mode optimizations for music controls */
@media (prefers-color-scheme: dark) {
  .music-control-mobile {
    color-scheme: dark;
  }

  .music-control-mobile button {
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
  }
}

/* PWA and mobile browser optimizations */
@supports (padding: env(safe-area-inset-bottom)) {
  .music-control-mobile {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
  .music-control-mobile input[type="range"] {
    -webkit-appearance: none;
    background: transparent;
  }

  .music-control-mobile input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: hsl(var(--primary));
    cursor: pointer;
  }
}

/* Android specific optimizations */
@media (hover: none) and (pointer: coarse) {
  .music-control-mobile button:hover {
    background-color: transparent;
  }

  .music-control-mobile button:active {
    background-color: hsl(var(--muted));
  }
}
